export function parseQuery(search: string): Record<string, string> {
  return search
    .substring(1)
    .split("&")
    .reduce((acc, pair) => {
      const [key, value] = pair.split("=");
      if (key) acc[decodeURIComponent(key)] = decodeURIComponent(value || "");
      return acc;
    }, {} as Record<string, string>);
}

/**
 * 获取传入值的数据类型
 * @param {*} val 传入的值
 * @returns
 */
export function getTag(val: any) {
  if (val == null) return val === undefined ? "[object Undefined]" : "[object Null]";
  return Object.prototype.toString.call(val);
}

/**
 * 判断是否为null 包括 null undefined 'null' 'undefined' 'NULL' 'UNDEFINED'
 * @param {*} val 要判断的值
 * @returns {Boolean}
 */
export function isNull(val: unknown) {
  return !!("" + val).trim().match(/^(null)$|^(undefined)$/i);
}

/**
 * 判断是否为空 包括 null undefined 'null' 'undefined' 'NULL' 'UNDEFINED' '' {} '{}' [] '[]'
 * @param {*} val 要判断的值
 * @returns {Boolean}
 */
export function isEmpty(val: unknown) {
  if (isNull(val)) {
    return true;
  } else if (getTag(val) === "[object Date]") {
    const date: Date = val as Date;
    return isNaN(new Date(date).valueOf());
  } else if (getTag(val) === "[object Object]" && val) {
    return Object.keys(val).length === 0;
  } else {
    val = ("" + val).trim();
    return val === "" || val === "{}" || val === "[]";
  }
}

/**
 * 判断是否是真，仅当'false'、false、'0'、0和空值(isNull)时返回false
 * @param {Boolean} bool 要判断的值
 * @returns {Boolean}
 */
export function isTrue(bool: boolean | string | number) {
  return !!bool && !(bool === "false" || bool === "0" || isNull(bool));
}

/**
 * 验签过滤入参
 * 规则 非空 非0 非 false的值
 */
export function filterData(val: Record<string, unknown>): Record<string, unknown> {
  const keys = Object.keys(val).filter((key) => !isEmpty(val[key]) && isTrue(val[key] as string));
  return keys.reduce((pre: Record<string, unknown>, cur) => {
    pre[cur] = val[cur];
    return pre;
  }, {});
}

/**
 * 根据url生成query对象（目前暂时只支持单层级）
 */
export function getQuery(val: string = ""): Record<string, string> {
  // 转译
  val = decodeURIComponent(val);
  const list: Array<string> = val.replace("?", "").split("&");
  return list.reduce((pre: Record<string, string>, cur) => {
    const array = cur.split("=");
    if (array.length < 2) return pre;
    pre[array[0]] = array[1];
    return pre;
  }, {});
}

/**
 * 验签加密
 * @param data
 * @param terminal
 * @returns
 */

/**
 * 过滤对象属性，仅保留值为有效数字或字符串的属性
 * @param obj 待过滤的对象
 * @param options 过滤选项
 * @returns 过滤后的新对象
 */
export function filterObjectByValue<T extends object>(
  obj: T,
  options: {
    // 是否递归处理嵌套对象
    recursive?: boolean;
    // 是否保留空字符串
    keepEmptyString?: boolean;
  } = {}
): Partial<T> {
  const { recursive = true, keepEmptyString = false } = options;
  const result: Record<string, any> = {};

  for (const [key, value] of Object.entries(obj)) {
    // 处理数字类型（过滤NaN和Infinity）
    if (typeof value === "number") {
      if (Number.isFinite(value)) {
        result[key] = value;
      }
    }
    // 处理字符串类型
    else if (typeof value === "string") {
      if (keepEmptyString || value.trim() !== "") {
        result[key] = value;
      }
    }
    // 递归处理嵌套对象
    else if (recursive && typeof value === "object" && value !== null) {
      if (Array.isArray(value)) {
        // 处理数组
        const filteredArray = value.flatMap((item: any): (string | number)[] => {
          if (typeof item === "number") {
            return Number.isFinite(item) ? [item] : [];
          }
          if (typeof item === "string") {
            return keepEmptyString || item.trim() !== "" ? [item] : [];
          }
          return [];
        });

        if (filteredArray.length > 0) {
          result[key] = filteredArray;
        }
      } else {
        // 处理普通对象
        const nestedResult = filterObjectByValue(value, options);
        if (Object.keys(nestedResult).length > 0) {
          result[key] = nestedResult;
        }
      }
    }
  }

  return result as Partial<T>;
}

/**
 * 设备检测工具
 */
export const deviceDetector = {
  /**
   * 检测是否为iOS设备
   */
  isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  },

  /**
   * 检测是否为Android设备
   */
  isAndroid(): boolean {
    return /Android/.test(navigator.userAgent);
  },

  /**
   * 检测是否为移动设备
   */
  isMobile(): boolean {
    return this.isIOS() || this.isAndroid();
  },
  /**
   * 检测是否为Safari浏览器
   */
  isSafari(): boolean {
    return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  },

  /**
   * 获取设备类型
   */
  getDeviceType(): "ios" | "android" | "desktop" {
    if (this.isIOS()) return "ios";
    if (this.isAndroid()) return "android";
    return "desktop";
  },

  /**
   * 检测是否在微信中
   */
  isWeChat(): boolean {
    return /MicroMessenger/i.test(navigator.userAgent);
  },

  /**
   * 检测是否在QQ中
   */
  isQQ(): boolean {
    return /QQ/i.test(navigator.userAgent);
  },

  /**
   * 检测是否为Chrome浏览器
   */
  isChrome(): boolean {
    return /Chrome/i.test(navigator.userAgent) && !/Edge/i.test(navigator.userAgent);
  },

  /**
   * 检测是否为OnePlus手机
   */
  isOnePlus(): boolean {
    return /OnePlus|ONEPLUS/i.test(navigator.userAgent);
  },

  /**
   * 检测是否为OnePlus手机的Chrome浏览器
   */
  isOnePlusChrome(): boolean {
    return this.isOnePlus() && this.isChrome();
  },

  /**
   * 获取详细的设备信息
   */
  getDeviceInfo(): {
    deviceType: "ios" | "android" | "desktop";
    isOnePlus: boolean;
    isChrome: boolean;
    isOnePlusChrome: boolean;
    isSafari: boolean;
    isWeChat: boolean;
    isQQ: boolean;
    userAgent: string;
  } {
    return {
      deviceType: this.getDeviceType(),
      isOnePlus: this.isOnePlus(),
      isChrome: this.isChrome(),
      isOnePlusChrome: this.isOnePlusChrome(),
      isSafari: this.isSafari(),
      isWeChat: this.isWeChat(),
      isQQ: this.isQQ(),
      userAgent: navigator.userAgent,
    };
  },
};

/**
 * 应用下载工具
 */
export const appDownloader = {
  /**
   * 尝试打开app，失败后跳转应用商城
   * @param storeUrl 应用商城链接
   * @param platform 平台类型
   * @param timeout 超时时间（毫秒）
   */
  async openApp(
    storeUrl: string,
    platform: "ios" | "android",
    timeout: number = 3000
  ): Promise<boolean> {
    // 根据平台使用不同的唤起策略
    if (platform === "ios") {
      return this.openIOSApp(storeUrl, timeout);
    } else {
      return this.openAndroidApp(storeUrl, timeout);
    }
  },

  /**
   * iOS专用的app唤起方法
   * 直接跳转到应用商城
   */
  async openIOSApp(storeUrl: string, timeout: number = 3000): Promise<boolean> {
    return this.openAppStore(storeUrl, timeout);
  },

  /**
   * Android专用的app唤起方法
   * 直接跳转到应用商城
   */
  async openAndroidApp(storeUrl: string, timeout: number = 3000): Promise<boolean> {
    return this.openAppStore(storeUrl, timeout);
  },

  /**
   * 尝试打开应用商城
   * @param storeUrl 应用商城链接
   * @param timeout 超时时间（毫秒）
   */
  async openAppStore(storeUrl: string, timeout: number = 3000): Promise<boolean> {
    return new Promise((resolve) => {
      let isResolved = false;

      console.log("Attempting to open URL:", storeUrl);

      // 设置一个较短的成功超时 - 假设跳转成功
      const successTimer = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          console.log("URL opened successfully (assumed success)");
          resolve(true);
        }
      }, 1500);

      // 设置失败超时 - 只有在明确检测到失败时才使用
      const failureTimer = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          console.log("URL opening failed (timeout)");
          resolve(false);
        }
      }, timeout);

      try {
        // 尝试打开链接
        window.location.href = storeUrl;
        console.log("URL redirect initiated");

        // 监听页面可见性变化 - 如果页面变为隐藏，说明跳转成功
        const handleVisibilityChange = () => {
          if (!isResolved && document.visibilityState === "hidden") {
            clearTimeout(successTimer);
            clearTimeout(failureTimer);
            isResolved = true;
            console.log("URL opened successfully (visibility change detected)");
            resolve(true);
          }
        };

        // 监听页面卸载事件 - 如果页面卸载，说明跳转成功
        const handleBeforeUnload = () => {
          if (!isResolved) {
            clearTimeout(successTimer);
            clearTimeout(failureTimer);
            isResolved = true;
            console.log("URL opened successfully (page unload detected)");
            resolve(true);
          }
        };

        // 监听窗口失去焦点 - 可能是跳转到了应用商店
        const handleBlur = () => {
          if (!isResolved) {
            clearTimeout(successTimer);
            clearTimeout(failureTimer);
            isResolved = true;
            console.log("URL opened successfully (window blur detected)");
            resolve(true);
          }
        };

        document.addEventListener("visibilitychange", handleVisibilityChange);
        window.addEventListener("beforeunload", handleBeforeUnload);
        window.addEventListener("blur", handleBlur);

        // 清理事件监听器
        setTimeout(() => {
          document.removeEventListener("visibilitychange", handleVisibilityChange);
          window.removeEventListener("beforeunload", handleBeforeUnload);
          window.removeEventListener("blur", handleBlur);
        }, timeout);
      } catch (error) {
        console.error("Failed to open URL:", error);
        clearTimeout(successTimer);
        clearTimeout(failureTimer);
        if (!isResolved) {
          isResolved = true;
          resolve(false);
        }
      }
    });
  },

  /**
   * 显示Toast提示
   * @param message 提示信息
   * @param duration 显示时长（毫秒）
   */
  showToast(message: string, duration: number = 3000): void {
    // 移除已存在的toast
    const existingToast = document.querySelector(".app-download-toast");
    if (existingToast) {
      existingToast.remove();
    }

    // 创建toast元素
    const toast = document.createElement("div");
    toast.className = "app-download-toast";
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 14px;
      z-index: 9999;
      max-width: 80%;
      text-align: center;
      word-wrap: break-word;
    `;

    // 添加到页面
    document.body.appendChild(toast);

    // 自动移除
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, duration);
  },
};
