import type { ImportMetaEnv } from "../../env.d.ts";

// Define Recordable if not already defined elsewhere
type Recordable<T = any> = Record<string, T>;

// Read all environment variable configuration files to process.env
export function wrapperEnv(envConf: Recordable): ImportMetaEnv {
  const ret: any = {};

  for (const envName of Object.keys(envConf)) {
    let realName = envConf[envName].replace(/\\n/g, "\n");

    // 布尔值转换
    if (realName === "true") {
      realName = true;
    } else if (realName === "false") {
      realName = false;
    }

    // 数字类型转换
    if (envName === "VITE_PORT" || envName === "VITE_API_TIMEOUT") {
      realName = Number(realName);
    }

    // JSON 类型转换
    if (envName === "VITE_PROXY") {
      try {
        realName = JSON.parse(realName);
      } catch (error) {
        realName = [];
      }
    }

    // 布尔值字符串转换为布尔值
    if (envName === "VITE_DROP_CONSOLE" || envName === "VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE") {
      realName = realName === "true" || realName === true;
    }

    ret[envName] = realName;
    process.env[envName] = realName;
  }
  return ret;
}
