FROM swr.ap-southeast-3.myhuaweicloud.com/app_ns/web-node:22.14.0-obsutil AS build

ARG buildEnv=dev
ENV mybuildEnv=${buildEnv}

WORKDIR /opt/www/h5
COPY . /opt/www/h5

# npm run build:${mybuildEnv}
RUN npm install --ignore-engines --prefer-offline \
    --registry=https://registry.npmmirror.com && \
    npm run build-only

FROM nginx:1.26.0

ARG bucket=xxx
ARG obsParams=xxx

ENV mybucket=${bucket}
ENV myobsParams=${obsParams}

WORKDIR /opt/www/h5
COPY --from=build /opt/www/h5 .

RUN /opt/www/h5/obsutil config ${myobsParams} && \
    /opt/www/h5/obsutil cp /opt/www/h5/dist obs://${mybucket} \
    -exclude '*.gz' -r -f -flat && rm -rf /opt/www/h5/obsutil

EXPOSE 80 443