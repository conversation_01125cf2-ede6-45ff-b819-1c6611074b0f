<script setup lang="ts">
import { onMounted } from "vue";

const MAYA_LOGIN = "/mayalogin";

// 换起APP
function toApp() {
  setTimeout(() => {
    try {
      // 默认跳转Android app
      const appUrl = import.meta.env.VITE_JUMP_APP_URL;
      location.href = appUrl + MAYA_LOGIN;
      const isFirefoxOrChrome = /Firefox|Chrome/.test(navigator.userAgent);
      if (isFirefoxOrChrome) {
        window.location.href = "about:blank";
        window.close();
      } else {
        window.opener = null;
        window.open("", "_self");
        window.close();
      }
    } catch (error) {
      console.log("error", error);
    }
  }, 2000);
}

onMounted(() => {
  toApp();
});
</script>

<template>
  <div class="loading-page">
    <div class="loading-content">
      <img src="@/assets/img/Nustar.png" alt="" />
      <!-- Spinner Loading -->
      <div class="spinner-container">
        <div class="spinner"></div>
      </div>

      <!-- Loading Text -->
      <div class="loading-text">Loading...</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.loading-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  z-index: 9999;
  font-family: "Inter", sans-serif;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 40px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  width: 100%;

  img {
    width: 160px;
    height: auto;
  }
}

// Spinner Loading
.spinner-container {
  width: 60px;
  height: 60px;
}

.spinner {
  width: 100%;
  height: 100%;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #ba1245;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Loading Text
.loading-text {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  text-align: center;
}
</style>
