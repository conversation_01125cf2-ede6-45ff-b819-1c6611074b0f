import axios from "../request/index";

export const getPayStatus = async (params: {
  order_id: string;
  user_id: string;
  sign: string;
  timestamp: string;
  pay_channel: string;
}) => {
  const res = await axios.request({
    url: "/open/api/notify/web/payment/status",
    params,
    method: "GET",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
    },
  });
  return res.data;
};

// 获取app下载页面配置
export const getAppDownloadConfig = async (params = {}) => {
  try {
    const res = await axios.request({
      url: "/open/api/base/landingPage",
      method: "GET",
      params,
      headers: {
        "Content-Type": "application/json",
      },
    });

    return res.data;
  } catch (error) {
    throw error;
  }
};

// 获取IOS跳转维护链接配置
export const getIOSJumpLink = async () => {
  const res = await axios.request({
    url: "/open/api/base/globalConfig",
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
  return res.data;
};
