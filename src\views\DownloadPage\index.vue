<script setup lang="ts" name="AppDownload">
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { getAppDownloadConfig } from "@/service";
import Loading from "@/components/Loading.vue";
import { getQuery, appDownloader } from "@/utils";
import { envConfig } from "@/utils/env";
import AndroidIcon from "@/assets/img/download/Android.png";
import iosIcon from "@/assets/img/download/iOS.png";
import webIcon from "@/assets/img/download/Web.png";

// 按钮配置类型
interface ButtonConfig {
  switch: boolean;
  text: string;
  storeUrl: string;
}

// 页面配置数据类型
interface DownloadConfig {
  picture_url: string;
  slogan: string;
  iosButton: ButtonConfig;
  androidButton: ButtonConfig;
  webButton: ButtonConfig;
}

const router = useRouter();
const loading = ref(false);
const config = ref<DownloadConfig | null>(null);
const isDownloading = ref(false);

// 计算显示的按钮数量
const activeButtonsCount = computed(() => {
  if (!config.value) return 0;
  return [
    config.value.iosButton.switch,
    config.value.androidButton.switch,
    config.value.webButton.switch,
  ].filter(Boolean).length;
});

// 初始化页面数据
async function initPageData() {
  loading.value = true;
  try {
    const query = getQuery(window.location.search);
    const res = await getAppDownloadConfig({ channel_type: query.channel });

    if (res.code === 0 && res.data) {
      config.value = {
        picture_url: res.data.picture_url ? envConfig.assetsUrl + res.data.picture_url : "",
        slogan: res.data.slogan || "",
        iosButton: {
          switch: res.data.ios_switch,
          text: res.data.iosButtonText || "iOS",
          storeUrl: res.data.ios_download_url,
        },
        androidButton: {
          switch: res.data.android_switch,
          text: res.data.androidButtonText || "Android",
          storeUrl: res.data.android_download_url,
        },
        webButton: {
          switch: res.data.web_switch,
          text: res.data.webButtonText || "Web",
          storeUrl: res.data.web_url,
        },
      };
    } else {
      config.value = getDefaultConfig();
    }
  } catch (error) {
    console.log("API not available, using default config");
    config.value = getDefaultConfig();
  } finally {
    loading.value = false;
  }
}

// 获取默认配置
function getDefaultConfig(): DownloadConfig {
  return {
    picture_url: "",
    slogan: "",
    iosButton: {
      switch: false,
      text: "iOS",
      storeUrl: "",
    },
    androidButton: {
      switch: false,
      text: "Android",
      storeUrl: "",
    },
    webButton: {
      switch: false,
      text: "Web",
      storeUrl: "",
    },
  };
}

// 处理下载按钮点击
async function handleDownload(platform: "ios" | "android" | "web") {
  if (isDownloading.value || !config.value) return;

  const buttonConfig = config.value[`${platform}Button`];

  if (!buttonConfig.storeUrl && platform !== "ios") return;

  isDownloading.value = true;
  try {
    if (platform === "web") {
      window.location.href = buttonConfig.storeUrl;
    } else if (platform === "ios") {
      router.push("/notSupported"); // ios跳转暂不支持页面
    } else {
      const success = await appDownloader.openApp(buttonConfig.storeUrl, platform, 3000);
      if (!success) {
        appDownloader.showToast("Download failed, please try again.");
      }
    }
  } catch (error) {
    console.error("Download error:", error);
    // appDownloader.showToast("Download failed, please try again.");
  } finally {
    isDownloading.value = false;
  }
}

// 页面挂载时初始化
// 按钮配置数据
const buttonConfigs = computed(() =>
  [
    {
      platform: "ios" as const,
      config: config.value?.iosButton,
      icon: iosIcon,
      class: "ios-btn",
      singleText: "Download",
    },
    {
      platform: "android" as const,
      config: config.value?.androidButton,
      icon: AndroidIcon,
      class: "android-btn",
      singleText: "Download",
    },
    {
      platform: "web" as const,
      config: config.value?.webButton,
      icon: webIcon,
      class: "web-btn",
      singleText: "Web",
    },
  ].filter((item) => item.config?.switch)
);

onMounted(() => {
  initPageData();
});
</script>

<template>
  <Loading v-if="loading" />
  <div v-else class="page-content">
    <div class="download-page">
      <!-- 主要内容区域 -->
      <div class="content-area">
        <img v-if="config?.picture_url" :src="config?.picture_url" alt="" />
        <div v-else class="no-data">
          <div>Oops</div>
          <div>NO DATA</div>
        </div>
      </div>

      <!-- 底部固定下载模块 -->
      <div class="download-section" v-if="activeButtonsCount > 0 || config?.slogan?.length > 0">
        <div class="download-buttons">
          <div
            v-for="item in buttonConfigs"
            :key="item.platform"
            class="download-btn"
            :class="[item.class, { downloading: isDownloading }]"
            :disabled="isDownloading && item.platform !== 'web'"
            @click="handleDownload(item.platform)"
          >
            <div class="btn-content">
              <div class="btn-icon">
                <img :src="item.icon" alt="" />
              </div>
              <div class="btn-text">
                <div class="btn-subtitle">
                  {{ activeButtonsCount > 1 ? item.config?.text : item.singleText }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="config?.slogan" class="slogan-text">
          {{ config.slogan }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-content {
  padding-bottom: constant(safe-area-inset-bottom); /* iOS < 11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* iOS >= 11.2 */
  height: 100dvh; /* 动态视口高度，避免iOS Safari地址栏影响 */
  height: 100vh;
  max-width: 100vw;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: flex-start;
}

.download-page {
  flex: 1;
  max-height: 90vh;
  max-height: 100dvh; /* 动态视口高度，避免iOS Safari地址栏影响 */
  width: 100vw;
  height: 100%;
  font-family: "Inter";
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.content-area {
  flex: 1;
  max-height: 100%;
  width: 100%;
  // overflow-y: auto;
  padding-bottom: 120px;
  overflow: auto;
  img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
    min-height: 100%;
  }
}

.no-data {
  width: 100%;
  margin: 240px auto;
  color: #666;
  font-size: 18px;
  text-align: center;

  div:last-child {
    font-size: 12px;
    color: #999;
    margin-top: 20px;
  }
}

.download-section {
  font-family: "Inter";
  background: #fff;
  padding: 10px;
  width: 100%;
  opacity: 1;
  gap: 6px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.1);
  flex-shrink: 0; /* 防止被压缩 */
  margin-top: auto; /* 推到底部 */
  // padding-bottom: calc(20px + constant(safe-area-inset-bottom)); /* iOS < 11.2 一加手机谷歌浏览器 */
  // padding-bottom: calc(20px + env(safe-area-inset-bottom)); /* iOS >= 11.2 一加手机谷歌浏览器 */
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  transform: translate(0, 0); /* 添加transform可以提高定位精度 */
}

.download-buttons {
  display: flex;
  gap: 8px;
  // 确保按钮始终并排，不换行
  flex-wrap: nowrap;
  width: 100%;
}

.download-btn {
  flex: 1;
  background: #222;
  border: none;
  border-radius: 30px;
  width: 163px;
  height: 54px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  // transition: opacity 0.2s;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.downloading {
    opacity: 0.8;
  }
}

.btn-content {
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 28px;
    height: 28px;
  }
}

.btn-text {
  display: flex;
  align-items: center;
  margin-left: 8px;
}

.btn-subtitle {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #fff;
}

.slogan-text {
  font-size: 13px;
  line-height: 20px;
  text-align: center;
  color: #666;
  word-wrap: break-word;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  max-height: 60px;
}
</style>
