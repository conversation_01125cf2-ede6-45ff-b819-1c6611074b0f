<script setup lang="ts">
defineOptions({
    name: 'Loading'
})
</script>

<template>
    <div class="loading-container">
        <div class="loading-spinner">
            <div class="spinner"></div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.loading-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    position: relative;
}

.spinner {
    width: 100%;
    height: 100%;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2B89F6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>
