<script lang="ts" setup name="App">
import { RouterView } from "vue-router";
import { envConfig } from "@/utils/env";

// 仅在开发环境和测试环境启用 vConsole
if (envConfig.enableVConsole) {
  import("vconsole").then((vConsole) => {
    new vConsole.default();
  });
}
</script>

<template>
  <div class="app">
    <router-view v-slot="{ Component }">
      <keep-alive :include="['download']">
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>
</template>

<style lang="scss">
.app {
  width: 100%;
}
</style>
