/**
 * 获取本机的 IP 地址
 * @returns {string} 本机 IP 地址，如果未找到则返回 'localhost'
 */
import { networkInterfaces } from "os";
export function getLocalIP() {
  const nets = networkInterfaces();
  const results = {};

  // 遍历所有网络接口
  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      // 跳过 IPv6 和回环地址
      if (net.family === "IPv4" && !net.internal) {
        if (!results[name]) {
          results[name] = [];
        }
        results[name].push(net.address);
      }
    }
  }

  // 尝试获取常用网络接口的 IP
  const interfaceOrder = ["en0", "eth0", "Wi-Fi", "以太网"];
  for (const intf of interfaceOrder) {
    if (results[intf] && results[intf].length > 0) {
      return results[intf][0];
    }
  }

  // 如果没有找到常用接口，返回第一个找到的非回环 IPv4 地址
  const addresses = Object.values(results).flat();
  if (addresses.length > 0) {
    return addresses[0];
  }

  // 如果没有找到任何非回环地址，返回 localhost
  return "localhost";
}
